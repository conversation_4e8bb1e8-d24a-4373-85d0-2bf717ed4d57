from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, EmailStr
from typing import Optional
import requests
import os
from utils.logger import logger

router = APIRouter()

class PostHogSignupRequest(BaseModel):
    user_id: str
    email: EmailStr
    signup_method: Optional[str] = "email"
    created_at: str
    name: Optional[str] = None

class AnalyticsResponse(BaseModel):
    success: bool
    message: str

@router.post("/track-signup", response_model=AnalyticsResponse)
async def track_signup(request: PostHogSignupRequest):
    """
    Track user signup event in PostHog
    This endpoint is called by the database trigger when a new user is created
    """
    try:
        posthog_api_key = os.getenv("POSTHOG_API_KEY")
        posthog_host = os.getenv("POSTHOG_HOST", "https://app.posthog.com")
        
        if not posthog_api_key:
            logger.warning("POSTHOG_API_KEY not found in environment variables")
            return AnalyticsResponse(
                success=False,
                message="PostHog API key not configured"
            )
        
        # PostHog event data
        event_data = {
            "api_key": posthog_api_key,
            "event": "user_signed_up",
            "properties": {
                "user_id": request.user_id,
                "email": request.email,
                "signup_method": request.signup_method,
                "created_at": request.created_at,
                "is_first_time_signup": True
            },
            "distinct_id": request.user_id
        }
        
        # Also identify the user with their properties
        identify_data = {
            "api_key": posthog_api_key,
            "event": "$identify",
            "properties": {
                "$set": {
                    "email": request.email,
                    "signup_date": request.created_at,
                    "signup_method": request.signup_method,
                    "user_id": request.user_id
                }
            },
            "distinct_id": request.user_id
        }
        
        # Send both events to PostHog
        posthog_url = f"{posthog_host}/capture/"
        
        # Send signup event
        signup_response = requests.post(
            posthog_url,
            json=event_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        # Send identify event
        identify_response = requests.post(
            posthog_url,
            json=identify_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if signup_response.status_code == 200 and identify_response.status_code == 200:
            logger.info(f"Successfully tracked signup for user {request.user_id} ({request.email})")
            return AnalyticsResponse(
                success=True,
                message="Signup event tracked successfully"
            )
        else:
            logger.error(f"PostHog API error - Signup: {signup_response.status_code}, Identify: {identify_response.status_code}")
            return AnalyticsResponse(
                success=False,
                message="Failed to track signup event"
            )
            
    except Exception as e:
        logger.error(f"Error tracking signup for user {request.user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while tracking signup"
        )

@router.post("/track-signup-background", response_model=AnalyticsResponse)
async def track_signup_background(request: PostHogSignupRequest):
    """
    Track user signup event in PostHog (background processing)
    This endpoint is called by the database trigger for non-blocking execution
    """
    try:
        logger.info(f"Queuing PostHog signup tracking for {request.email}")
        
        def track_signup_sync():
            posthog_api_key = os.getenv("POSTHOG_API_KEY")
            posthog_host = os.getenv("POSTHOG_HOST", "https://app.posthog.com")
            
            if not posthog_api_key:
                logger.warning("POSTHOG_API_KEY not found in environment variables")
                return False
            
            # PostHog event data
            event_data = {
                "api_key": posthog_api_key,
                "event": "user_signed_up",
                "properties": {
                    "user_id": request.user_id,
                    "email": request.email,
                    "signup_method": request.signup_method,
                    "created_at": request.created_at,
                    "is_first_time_signup": True
                },
                "distinct_id": request.user_id
            }
            
            # Also identify the user with their properties
            identify_data = {
                "api_key": posthog_api_key,
                "event": "$identify",
                "properties": {
                    "$set": {
                        "email": request.email,
                        "signup_date": request.created_at,
                        "signup_method": request.signup_method,
                        "user_id": request.user_id
                    }
                },
                "distinct_id": request.user_id
            }
            
            try:
                # Send both events to PostHog
                posthog_url = f"{posthog_host}/capture/"
                
                # Send signup event
                signup_response = requests.post(
                    posthog_url,
                    json=event_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                # Send identify event
                identify_response = requests.post(
                    posthog_url,
                    json=identify_data,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if signup_response.status_code == 200 and identify_response.status_code == 200:
                    logger.info(f"Successfully tracked signup for user {request.user_id} ({request.email})")
                    return True
                else:
                    logger.error(f"PostHog API error - Signup: {signup_response.status_code}, Identify: {identify_response.status_code}")
                    return False
                    
            except Exception as e:
                logger.error(f"Error in background PostHog tracking: {str(e)}")
                return False
        
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            executor.submit(track_signup_sync)
        
        return AnalyticsResponse(
            success=True,
            message="Signup tracking queued for processing"
        )
            
    except Exception as e:
        logger.error(f"Error queuing PostHog tracking for user {request.user_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while queuing signup tracking"
        )
